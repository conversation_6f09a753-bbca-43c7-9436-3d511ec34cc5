2025-06-09 12:46:59.910 | INFO     | utils.logging:setup_logging:153 | [PID:453061] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 12:46:59.910 | INFO     | utils.logging:setup_logging:157 | [PID:453061] Worker logging setup complete - Worker ID: main
2025-06-09 12:46:59.910 | INFO     | __main__:cli:98 | FinRL Trading Agent CLI initialized
2025-06-09 12:47:03.345 | INFO     | __main__:tune:312 | Starting hyperparameter tuning
2025-06-09 12:47:03.453 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 12:47:03.453 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 12:47:03.455 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 12:47:03.471 | INFO     | utils.logging:info:191 | [PID:453061] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 12:47:05.229 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7216
2025-06-09 12:47:05.233 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 12:47:05.248 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.015s
2025-06-09 12:47:05.249 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 12:47:05.249 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.016s
2025-06-09 12:47:05.249 | INFO     | utils.logging:info:191 | [PID:453061] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 12:47:05.249 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 12:47:05.263 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 12:47:05.263 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.014s
2025-06-09 12:47:05.264 | INFO     | utils.logging:info:191 | [PID:453061] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 12:47:05.274 | INFO     | utils.logging:info:191 | [PID:453061] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 12:47:05.279 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 12:47:05.279 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 12:47:05.299 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 12:47:05.325 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 12:47:05.325 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 1.871s
2025-06-09 12:47:05.326 | INFO     | __main__:tune:333 | --- Quick Stats for processed_for_env_df (after DataProcessor.process_stock_data) ---
2025-06-09 12:47:05.326 | INFO     | __main__:tune:334 | Shape: (23370, 45)
2025-06-09 12:47:05.326 | INFO     | __main__:tune:336 | Columns: ['date', 'open', 'high', 'low', 'close', 'volume', 'dividends', 'stock splits', 'tic', 'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'rsi_14', 'cci_20', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'turbulence', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 12:47:05.334 | INFO     | __main__:tune:339 | NaN counts per column (if any):
Series([], dtype: int64)
2025-06-09 12:47:05.335 | INFO     | __main__:tune:340 | Inf counts per column (if any):
Series([], dtype: int64)
2025-06-09 12:47:05.336 | INFO     | __main__:tune:342 | Date range in processed_for_env_df ('date'): 2016-01-04 00:00:00 to 2025-04-17 00:00:00
2025-06-09 12:47:05.336 | INFO     | __main__:tune:344 | Unique 'tic' in processed_for_env_df: 10
2025-06-09 12:47:05.336 | INFO     | __main__:tune:347 | --- End Quick Stats for processed_for_env_df ---
2025-06-09 12:47:05.337 | INFO     | __main__:tune:358 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 12:47:05.347 | INFO     | __main__:tune:377 | Training data: 18690 records, Validation data: 4680 records
2025-06-09 12:47:05.347 | INFO     | __main__:tune:424 | Transforming data format for FinRL environment compatibility
2025-06-09 12:47:05.365 | INFO     | __main__:tune:428 | Train_df columns before deduplication (showing only duplicated ones): []
2025-06-09 12:47:05.367 | INFO     | __main__:tune:430 | Train_df columns after deduplication: 45
2025-06-09 12:47:05.367 | INFO     | __main__:tune:432 | Val_df columns before deduplication (showing only duplicated ones): []
2025-06-09 12:47:05.368 | INFO     | __main__:tune:434 | Val_df columns after deduplication: 45
2025-06-09 12:47:05.368 | INFO     | __main__:tune:436 | Transformed training data: 18690 records, Validation data: 4680 records
2025-06-09 12:47:05.369 | INFO     | __main__:tune:447 | Training data index: day, shape: (18690, 45)
2025-06-09 12:47:05.369 | INFO     | __main__:tune:448 | Validation data index: day, shape: (4680, 45)
2025-06-09 12:47:05.369 | INFO     | __main__:tune:457 | ✓ training data has correct 'day' index for FinRL (range: 0 to 1868)
2025-06-09 12:47:05.370 | INFO     | __main__:tune:457 | ✓ validation data has correct 'day' index for FinRL (range: 0 to 467)
2025-06-09 12:47:05.370 | INFO     | __main__:tune:460 | Applying NaN fill and type check for technical indicators in training data.
2025-06-09 12:47:05.385 | INFO     | __main__:tune:475 | Applying NaN fill and type check for technical indicators in validation data.
2025-06-09 12:47:05.413 | INFO     | __main__:tune:588 | Environment created: state_dim=431, action_dim=10
2025-06-09 12:47:05.413 | INFO     | utils.logging:__enter__:344 | Starting Hyperparameter Optimization | Context: 
2025-06-09 12:47:05.413 | INFO     | utils.logging:info:191 | Starting optimization with config: {'n_trials': 10, 'timeout': None, 'n_jobs': 1, 'sampler': 'tpe', 'pruner': 'median', 'direction': 'maximize', 'study_name': 'sac_trading_optimization', 'storage': 'sqlite:///optuna_studies.db', 'load_if_exists': False, 'directions': None, 'pruning_warmup_steps': 10, 'pruning_interval_steps': 50, 'early_stopping_rounds': None, 'early_stopping_threshold': None, 'learning_rate_range': (1e-05, 0.01), 'batch_size_choices': [64, 128, 256, 512], 'gamma_range': (0.9, 0.999), 'tau_range': (0.001, 0.01), 'alpha_range': (0.1, 0.5), 'hidden_sizes_choices': [[128, 128], [256, 256], [512, 256], [256, 256, 128]]}
2025-06-09 12:47:05.660 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.00010334384169604976, 'batch_size': 128, 'net_dims': [128, 64], 'gamma': 0.993394706416294, 'soft_update_tau': 0.0015707173338914732, 'alpha': 0.13139917306023538}
2025-06-09 12:47:05.660 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 12:47:05.660 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 12:47:05.660 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 12:47:06.429 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [128, 64], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.993394706416294, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.00010334384169604976, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 128, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 12:47:06.429 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 12:47:06.431 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 12:47:06.432 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 12:47:06.432 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 12:47:06.432 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 12:47:06.433 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 12:47:06.433 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 12:47:06.433 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 12:47:06.433 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 12:47:06.433 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 12:49:08.244 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 12:49:08.245 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 122.585s
2025-06-09 12:49:23.613 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.0009093264040654015, 'batch_size': 512, 'net_dims': [64, 64], 'gamma': 0.9771867179217213, 'soft_update_tau': 0.008078412321386215, 'alpha': 0.2982166613196865}
2025-06-09 12:49:23.614 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 12:49:23.614 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 12:49:23.614 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 12:49:23.619 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [64, 64], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9771867179217213, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.0009093264040654015, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 512, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 12:49:23.619 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 12:49:23.623 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 12:49:23.623 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 12:49:23.624 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 12:49:23.624 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 12:49:23.624 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 12:49:23.624 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 12:49:23.624 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 12:49:23.625 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 12:49:23.625 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 12:52:04.798 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 12:52:04.798 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 161.184s
2025-06-09 12:52:20.167 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.0027094959198005306, 'batch_size': 64, 'net_dims': [256, 256], 'gamma': 0.9214290005383855, 'soft_update_tau': 0.0048995940750611025, 'alpha': 0.17971306797571196}
2025-06-09 12:52:20.168 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 12:52:20.168 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 12:52:20.169 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 12:52:20.182 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [256, 256], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9214290005383855, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.0027094959198005306, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 64, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 12:52:20.182 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 12:52:20.187 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 12:52:20.188 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 12:52:20.189 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 12:52:20.189 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 12:52:20.190 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 12:52:20.190 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 12:52:20.190 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 12:52:20.191 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 12:52:20.191 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 12:54:33.911 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 12:54:33.911 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 133.743s
2025-06-09 12:54:56.990 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.007240980366816915, 'batch_size': 64, 'net_dims': [256, 256], 'gamma': 0.914572971715059, 'soft_update_tau': 0.008724040751133345, 'alpha': 0.29756694504201947}
2025-06-09 12:54:56.991 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 12:54:56.991 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 12:54:56.992 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 12:54:57.005 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [256, 256], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.914572971715059, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.007240980366816915, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 64, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 12:54:57.005 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 12:54:57.009 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 12:54:57.011 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 12:54:57.011 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 12:54:57.011 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 12:54:57.012 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 12:54:57.012 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 12:54:57.013 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 12:54:57.013 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 12:54:57.014 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 12:57:25.241 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 12:57:25.242 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 148.251s
2025-06-09 12:57:40.576 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.0003206070687251814, 'batch_size': 64, 'net_dims': [128, 64], 'gamma': 0.9846974926048303, 'soft_update_tau': 0.006053930146735184, 'alpha': 0.24060668701354754}
2025-06-09 12:57:40.576 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 12:57:40.577 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 12:57:40.577 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 12:57:40.583 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [128, 64], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9846974926048303, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.0003206070687251814, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 64, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 12:57:40.584 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 12:57:40.587 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 12:57:40.588 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 12:57:40.588 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 12:57:40.588 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 12:57:40.588 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 12:57:40.589 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 12:57:40.589 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 12:57:40.589 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 12:57:40.589 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 13:00:03.049 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 13:00:03.053 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 142.477s
2025-06-09 13:00:24.840 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.0018588198830765722, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9790923302417917, 'soft_update_tau': 0.0043319720662352255, 'alpha': 0.1062816732756839}
2025-06-09 13:00:24.841 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 13:00:24.841 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 13:00:24.841 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 13:00:24.848 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [64, 64], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9790923302417917, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.0018588198830765722, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 256, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 13:00:24.849 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 13:00:24.852 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 13:00:24.853 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 13:00:24.853 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 13:00:24.854 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 13:00:24.854 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 13:00:24.854 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 13:00:24.854 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 13:00:24.855 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 13:00:24.855 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 13:02:36.371 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 13:02:36.371 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 131.530s
2025-06-09 13:02:57.768 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.0006590711772769139, 'batch_size': 512, 'net_dims': [64, 64], 'gamma': 0.9384680932701329, 'soft_update_tau': 0.006695058253062235, 'alpha': 0.18508942254867694}
2025-06-09 13:02:57.769 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 13:02:57.769 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 13:02:57.769 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 13:02:57.774 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [64, 64], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9384680932701329, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.0006590711772769139, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 512, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 13:02:57.775 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 13:02:57.780 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 13:02:57.781 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 13:02:57.782 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 13:02:57.782 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 13:02:57.782 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 13:02:57.783 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 13:02:57.783 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 13:02:57.783 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 13:02:57.783 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 13:05:09.538 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 13:05:09.539 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 131.770s
2025-06-09 13:05:25.589 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.00010714437525817493, 'batch_size': 256, 'net_dims': [256, 128], 'gamma': 0.9380218065887349, 'soft_update_tau': 0.001102064687015839, 'alpha': 0.2396563324833316}
2025-06-09 13:05:25.589 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 13:05:25.590 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 13:05:25.591 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 13:05:25.598 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [256, 128], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9380218065887349, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.00010714437525817493, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 256, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 13:05:25.598 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 13:05:25.604 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 13:05:25.605 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 13:05:25.605 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 13:05:25.605 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 13:05:25.606 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 13:05:25.606 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 13:05:25.606 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 13:05:25.606 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 13:05:25.607 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 13:07:26.550 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 13:07:26.550 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 120.960s
2025-06-09 13:07:49.385 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 6.997207545114835e-05, 'batch_size': 128, 'net_dims': [128, 64], 'gamma': 0.9988320973000764, 'soft_update_tau': 0.0010011362350858977, 'alpha': 0.10109743594894482}
2025-06-09 13:07:49.386 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 13:07:49.386 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 13:07:49.386 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 13:07:49.396 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [128, 64], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9988320973000764, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 6.997207545114835e-05, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 128, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 13:07:49.396 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 13:07:49.400 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 13:07:49.401 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 13:07:49.401 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 13:07:49.401 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 13:07:49.402 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 13:07:49.402 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 13:07:49.402 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 13:07:49.402 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 13:07:49.402 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 13:10:11.763 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 13:10:11.764 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 142.378s
2025-06-09 13:10:35.441 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.00010369577914160006, 'batch_size': 128, 'net_dims': [128, 64], 'gamma': 0.9593599459911554, 'soft_update_tau': 0.0016425298715648116, 'alpha': 0.14016819028396355}
2025-06-09 13:10:35.441 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 13:10:35.442 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 13:10:35.442 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 13:10:35.453 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [128, 64], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9593599459911554, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.00010369577914160006, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 128, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 13:10:35.453 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 13:10:35.457 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 13:10:35.458 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 13:10:35.459 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 13:10:35.459 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 13:10:35.459 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 13:10:35.459 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 13:10:35.460 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 13:10:35.460 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 13:10:35.460 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 13:12:40.078 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 13:12:40.078 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 124.636s
2025-06-09 13:12:54.719 | INFO     | utils.logging:info:191 | Optimization results saved to models/checkpoints/optimization
2025-06-09 13:12:55.115 | WARNING  | utils.logging:warning:196 | Failed to generate some plots: unhashable type: 'list'
2025-06-09 13:12:55.118 | SUCCESS  | utils.logging:success:216 | Optimization completed: 16 trials, best value: 0.0
2025-06-09 13:12:55.120 | SUCCESS  | utils.logging:__exit__:352 | Completed Hyperparameter Optimization in 1549.707s
2025-06-09 13:12:55.120 | INFO     | utils.logging:wrapper:392 | Performance - HyperparameterOptimizer.optimize: 1549.707s
2025-06-09 13:12:55.120 | SUCCESS  | __main__:tune:635 | Hyperparameter tuning completed. Best params: {'learning_rate': 0.002190661086403672, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9067800051547014, 'soft_update_tau': 0.00108709998601217, 'alpha': 0.2655372017763429}
2025-06-09 13:12:55.121 | INFO     | __main__:tune:644 | Best hyperparameters saved to: models/checkpoints/best_hyperparameters.json
2025-06-09 17:59:28.467 | INFO     | utils.logging:setup_logging:153 | [PID:479897] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 17:59:28.471 | INFO     | utils.logging:setup_logging:157 | [PID:479897] Worker logging setup complete - Worker ID: main
2025-06-09 17:59:28.473 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 18:00:24.717 | INFO     | __main__:train:645 | Starting SAC agent training
2025-06-09 18:00:24.720 | INFO     | __main__:train:649 | NumPy error reporting set to 'raise' for all floating point issues.
2025-06-09 18:00:26.115 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 18:00:26.118 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 18:00:26.150 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 18:00:26.343 | INFO     | utils.logging:info:191 | [PID:479897] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:00:44.108 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7216
2025-06-09 18:00:44.114 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:00:44.155 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.039s
2025-06-09 18:00:44.155 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:00:44.156 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.042s
2025-06-09 18:00:44.157 | INFO     | utils.logging:info:191 | [PID:479897] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:00:44.157 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:00:44.178 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:00:44.179 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.022s
2025-06-09 18:00:44.182 | INFO     | utils.logging:info:191 | [PID:479897] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 18:00:44.198 | INFO     | utils.logging:info:191 | [PID:479897] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 18:00:44.208 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 18:00:44.209 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:00:44.246 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:00:44.291 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 18:00:44.291 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 18.142s
2025-06-09 18:00:44.292 | INFO     | __main__:train:697 | --- Quick Stats for processed_for_env_df (after DataProcessor.process_stock_data) ---
2025-06-09 18:00:44.292 | INFO     | __main__:train:698 | Shape: (23370, 45)
2025-06-09 18:00:44.293 | INFO     | __main__:train:700 | Columns: ['date', 'open', 'high', 'low', 'close', 'volume', 'dividends', 'stock splits', 'tic', 'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'rsi_14', 'cci_20', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'turbulence', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 18:00:44.304 | INFO     | __main__:train:703 | NaN counts per column (if any):
Series([], dtype: int64)
2025-06-09 18:00:44.305 | INFO     | __main__:train:704 | Inf counts per column (if any):
Series([], dtype: int64)
2025-06-09 18:00:44.306 | INFO     | __main__:train:706 | Date range in processed_for_env_df ('date'): 2016-01-04 00:00:00 to 2025-04-17 00:00:00
2025-06-09 18:00:44.307 | INFO     | __main__:train:708 | Unique 'tic' in processed_for_env_df: 10
2025-06-09 18:00:44.307 | INFO     | __main__:train:711 | --- End Quick Stats for processed_for_env_df ---
2025-06-09 18:00:44.308 | INFO     | __main__:train:722 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 18:00:44.318 | INFO     | __main__:train:741 | Training data: 18690 records, Validation data: 4680 records
2025-06-09 18:00:44.319 | INFO     | __main__:train:788 | Transforming data format for FinRL environment compatibility
2025-06-09 18:00:44.347 | INFO     | __main__:train:792 | Train_df columns before deduplication (showing only duplicated ones): []
2025-06-09 18:00:44.350 | INFO     | __main__:train:794 | Train_df columns after deduplication: 45
2025-06-09 18:00:44.351 | INFO     | __main__:train:796 | Val_df columns before deduplication (showing only duplicated ones): []
2025-06-09 18:00:44.352 | INFO     | __main__:train:798 | Val_df columns after deduplication: 45
2025-06-09 18:00:44.352 | INFO     | __main__:train:800 | Transformed training data: 18690 records, Validation data: 4680 records
2025-06-09 18:00:44.357 | INFO     | __main__:train:819 | Skipping set_index('date') as 'day' index is already set by prepare_finrl_data.
2025-06-09 18:00:44.357 | INFO     | __main__:train:822 | Applying NaN fill and type check for technical indicators in training data.
2025-06-09 18:00:44.377 | INFO     | __main__:train:852 | Applying NaN fill and type check for technical indicators in validation data.
2025-06-09 18:00:44.395 | INFO     | __main__:train:927 | About to create AsymmetricTradingEnv with config: stock_dim=10, action_space=10, df_shape=(18690, 45)
2025-06-09 18:00:44.408 | INFO     | __main__:train:929 | AsymmetricTradingEnv created successfully
2025-06-09 18:00:44.409 | INFO     | __main__:train:932 | About to reset training environment
2025-06-09 18:00:44.417 | INFO     | __main__:train:934 | Training environment reset successfully
2025-06-09 18:00:44.418 | INFO     | __main__:train:935 | About to get state from training environment
2025-06-09 18:00:44.421 | INFO     | __main__:train:938 | Got actual state from training environment: dim=431
2025-06-09 18:00:44.421 | INFO     | __main__:train:943 | Train_env actual_state_dim after init: 431
2025-06-09 18:00:44.422 | INFO     | __main__:train:957 | About to create validation AsymmetricTradingEnv with config: stock_dim=10, action_space=10
2025-06-09 18:00:44.428 | INFO     | __main__:train:959 | Validation AsymmetricTradingEnv created successfully
2025-06-09 18:00:44.429 | INFO     | __main__:train:962 | About to reset validation environment
2025-06-09 18:00:44.434 | INFO     | __main__:train:964 | Validation environment reset successfully
2025-06-09 18:00:44.434 | INFO     | __main__:train:965 | About to get state from validation environment
2025-06-09 18:00:44.436 | INFO     | __main__:train:968 | Got actual state from validation environment: dim=431
2025-06-09 18:00:44.436 | INFO     | __main__:train:971 | Val_env actual_state_dim after init: 431
2025-06-09 18:00:44.437 | INFO     | __main__:train:978 | Environment created: state_dim=431, action_dim=10
2025-06-09 18:00:44.437 | INFO     | __main__:train:995 | Creating SAC agent with state_dim=431, action_dim=10
2025-06-09 18:00:44.438 | ERROR    | __main__:train:1024 | SAC agent creation failed: 'SACConfig' object has no attribute 'target_step'
2025-06-09 18:00:44.438 | ERROR    | __main__:train:1072 | Training process failed: cannot access local variable 'agent_config' where it is not associated with a value
Traceback (most recent call last):

  File "/app/workspaces/finrl-bot/sonet/main.py", line 1005, in train
    'target_step': best_params.get('target_step', settings.sac.target_step),
                   │           │                  │        └ SACConfig(actor_hidden_sizes=[128, 128], critic_hidden_sizes=[128, 128], net_dims=[128, 128], learning_rate=0.0003, batch_siz...
                   │           │                  └ Settings(environment='development', debug=False, project_root=PosixPath('/app/workspaces/finrl-bot/sonet'), logging=LoggingCo...
                   │           └ <method 'get' of 'dict' objects>
                   └ {}

  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pydantic/main.py", line 991, in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')

AttributeError: 'SACConfig' object has no attribute 'target_step'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/app/workspaces/finrl-bot/sonet/main.py", line 1140, in <module>
    cli()
    └ <Group cli>

  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x712cecf52700>
           └ <Group cli>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x712cdebc3d50>
         │    └ <function Group.invoke at 0x712cecf53600>
         └ <Group cli>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x712cdebc3fd0>
           │               │       │       └ <function Command.invoke at 0x712cecf523e0>
           │               │       └ <Command train>
           │               └ <click.core.Context object at 0x712cdebc3fd0>
           └ <function Group.invoke.<locals>._process_result at 0x712cdf7b3240>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'config_override': None, 'resume': False, 'use_best_params': False}
           │   │      │    │           └ <click.core.Context object at 0x712cdebc3fd0>
           │   │      │    └ <function train at 0x712cdeb998a0>
           │   │      └ <Command train>
           │   └ <function Context.invoke at 0x712cecf51620>
           └ <click.core.Context object at 0x712cdebc3fd0>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'config_override': None, 'resume': False, 'use_best_params': False}
           │         └ ()
           └ <function train at 0x712cdeb998a0>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
           │ │                       │       └ {'config_override': None, 'resume': False, 'use_best_params': False}
           │ │                       └ ()
           │ └ <function get_current_context at 0x712cecf26660>
           └ <function train at 0x712cdeb99800>

> File "/app/workspaces/finrl-bot/sonet/main.py", line 1025, in train
    logger.error(f"Agent config: {agent_config}")
    │      └ <function Logger.error at 0x712ce115f2e0>
    └ <loguru.logger handlers=[(id=1, level=20, sink=<stdout>), (id=2, level=20, sink='logs/trading_agent_main.log')]>

UnboundLocalError: cannot access local variable 'agent_config' where it is not associated with a value
2025-06-09 18:15:16.421 | INFO     | utils.logging:setup_logging:153 | [PID:480919] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 18:15:16.422 | INFO     | utils.logging:setup_logging:157 | [PID:480919] Worker logging setup complete - Worker ID: main
2025-06-09 18:15:16.422 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 18:15:24.478 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 18:15:24.711 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 18:15:24.711 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 18:15:24.712 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 18:15:24.738 | INFO     | utils.logging:info:191 | [PID:480919] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:15:28.836 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7216
2025-06-09 18:15:28.843 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:15:28.886 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.042s
2025-06-09 18:15:28.886 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:15:28.887 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.044s
2025-06-09 18:15:28.888 | INFO     | utils.logging:info:191 | [PID:480919] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:15:28.889 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:15:28.907 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:15:28.907 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.019s
2025-06-09 18:15:28.912 | INFO     | utils.logging:info:191 | [PID:480919] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 18:15:28.927 | INFO     | utils.logging:info:191 | [PID:480919] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 18:15:28.938 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 18:15:28.938 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:15:28.977 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:15:29.029 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 18:15:29.030 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 4.317s
2025-06-09 18:15:29.031 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 18:15:29.045 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 18:15:29.045 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 18:15:29.071 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 18:15:29.093 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 18:17:00.318 | INFO     | utils.logging:setup_logging:153 | [PID:481042] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 18:17:00.318 | INFO     | utils.logging:setup_logging:157 | [PID:481042] Worker logging setup complete - Worker ID: main
2025-06-09 18:17:00.319 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 18:17:00.947 | INFO     | __main__:process_data:137 | Starting data processing
2025-06-09 18:17:00.947 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 18:17:00.947 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 18:17:00.947 | INFO     | __main__:process_data:157 | Fetching and processing data for 10 symbols...
2025-06-09 18:17:00.947 | INFO     | __main__:process_data:159 | Processing data for AAPL
2025-06-09 18:17:00.948 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:00.949 | INFO     | utils.logging:info:191 | Fetching data for AAPL from 2016-01-01 to 2025-04-19
2025-06-09 18:17:01.643 | INFO     | utils.logging:log_data_quality:270 | Data Quality - AAPL: 2337 points, 91.33% missing, 
2025-06-09 18:17:01.685 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.042s
2025-06-09 18:17:01.686 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for AAPL
2025-06-09 18:17:01.687 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.739s
2025-06-09 18:17:01.688 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:01.688 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:01.698 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:17:01.759 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:01.762 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:01.763 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:02.005 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.042s
2025-06-09 18:17:02.005 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2336 VIX records
2025-06-09 18:17:02.006 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.245s
2025-06-09 18:17:02.006 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:17:02.007 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:02.020 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:02.020 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.014s
2025-06-09 18:17:02.026 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 18:17:02.030 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:02.031 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:02.039 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:02.050 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:02.050 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.363s
2025-06-09 18:17:02.051 | SUCCESS  | __main__:process_data:180 | Successfully processed data for AAPL
2025-06-09 18:17:02.051 | INFO     | __main__:process_data:159 | Processing data for MSFT
2025-06-09 18:17:02.051 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:02.052 | INFO     | utils.logging:info:191 | Fetching data for MSFT from 2016-01-01 to 2025-04-19
2025-06-09 18:17:02.262 | INFO     | utils.logging:log_data_quality:270 | Data Quality - MSFT: 2337 points, 92.04% missing, 
2025-06-09 18:17:02.291 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.028s
2025-06-09 18:17:02.292 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for MSFT
2025-06-09 18:17:02.292 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.241s
2025-06-09 18:17:02.293 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:02.294 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:02.301 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:02.356 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:02.358 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:02.394 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.036s
2025-06-09 18:17:02.394 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:02.395 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.037s
2025-06-09 18:17:02.396 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:02.396 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:02.412 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:02.412 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.016s
2025-06-09 18:17:02.423 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:02.423 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:02.434 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:02.451 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:02.452 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.159s
2025-06-09 18:17:02.452 | SUCCESS  | __main__:process_data:180 | Successfully processed data for MSFT
2025-06-09 18:17:02.453 | INFO     | __main__:process_data:159 | Processing data for GOOGL
2025-06-09 18:17:02.453 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:02.453 | INFO     | utils.logging:info:191 | Fetching data for GOOGL from 2016-01-01 to 2025-04-19
2025-06-09 18:17:02.663 | INFO     | utils.logging:log_data_quality:270 | Data Quality - GOOGL: 2337 points, 90.82% missing, 
2025-06-09 18:17:02.694 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.031s
2025-06-09 18:17:02.695 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for GOOGL
2025-06-09 18:17:02.695 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.243s
2025-06-09 18:17:02.696 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:02.697 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:02.706 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:02.761 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:02.762 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:02.790 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.026s
2025-06-09 18:17:02.790 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:02.791 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.028s
2025-06-09 18:17:02.791 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:02.792 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:02.806 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:02.806 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.015s
2025-06-09 18:17:02.815 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:02.815 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:02.823 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:02.836 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:02.837 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.141s
2025-06-09 18:17:02.837 | SUCCESS  | __main__:process_data:180 | Successfully processed data for GOOGL
2025-06-09 18:17:02.837 | INFO     | __main__:process_data:159 | Processing data for AMZN
2025-06-09 18:17:02.838 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:02.839 | INFO     | utils.logging:info:191 | Fetching data for AMZN from 2016-01-01 to 2025-04-19
2025-06-09 18:17:03.060 | INFO     | utils.logging:log_data_quality:270 | Data Quality - AMZN: 2337 points, 91.40% missing, 
2025-06-09 18:17:03.091 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.031s
2025-06-09 18:17:03.091 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for AMZN
2025-06-09 18:17:03.092 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.254s
2025-06-09 18:17:03.093 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:03.093 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:03.102 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:03.181 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:03.182 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:03.200 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.018s
2025-06-09 18:17:03.200 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:03.201 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.019s
2025-06-09 18:17:03.201 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:03.201 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:03.211 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:03.211 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.010s
2025-06-09 18:17:03.217 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:03.217 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:03.223 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:03.234 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:03.235 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.142s
2025-06-09 18:17:03.235 | SUCCESS  | __main__:process_data:180 | Successfully processed data for AMZN
2025-06-09 18:17:03.235 | INFO     | __main__:process_data:159 | Processing data for META
2025-06-09 18:17:03.235 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:03.236 | INFO     | utils.logging:info:191 | Fetching data for META from 2016-01-01 to 2025-04-19
2025-06-09 18:17:03.395 | INFO     | utils.logging:log_data_quality:270 | Data Quality - META: 2337 points, 91.91% missing, 
2025-06-09 18:17:03.429 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.034s
2025-06-09 18:17:03.430 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for META
2025-06-09 18:17:03.431 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.195s
2025-06-09 18:17:03.431 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:03.432 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:03.443 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:03.495 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:03.497 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:03.521 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.024s
2025-06-09 18:17:03.521 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:03.521 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.025s
2025-06-09 18:17:03.521 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:03.522 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:03.535 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:03.535 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.014s
2025-06-09 18:17:03.543 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:03.543 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:03.550 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:03.560 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:03.561 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.129s
2025-06-09 18:17:03.561 | SUCCESS  | __main__:process_data:180 | Successfully processed data for META
2025-06-09 18:17:03.562 | INFO     | __main__:process_data:159 | Processing data for NVDA
2025-06-09 18:17:03.562 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:03.562 | INFO     | utils.logging:info:191 | Fetching data for NVDA from 2016-01-01 to 2025-04-19
2025-06-09 18:17:03.788 | INFO     | utils.logging:log_data_quality:270 | Data Quality - NVDA: 2337 points, 92.49% missing, 
2025-06-09 18:17:03.826 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.038s
2025-06-09 18:17:03.827 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for NVDA
2025-06-09 18:17:03.828 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.266s
2025-06-09 18:17:03.829 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:03.830 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:03.840 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:03.896 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:03.898 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:03.925 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.027s
2025-06-09 18:17:03.925 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:03.926 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.028s
2025-06-09 18:17:03.926 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:03.927 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:03.941 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:03.942 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.016s
2025-06-09 18:17:03.953 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:03.954 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:03.964 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:03.978 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:03.978 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.149s
2025-06-09 18:17:03.979 | SUCCESS  | __main__:process_data:180 | Successfully processed data for NVDA
2025-06-09 18:17:03.979 | INFO     | __main__:process_data:159 | Processing data for TSLA
2025-06-09 18:17:03.980 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:03.980 | INFO     | utils.logging:info:191 | Fetching data for TSLA from 2016-01-01 to 2025-04-19
2025-06-09 18:17:04.167 | INFO     | utils.logging:log_data_quality:270 | Data Quality - TSLA: 2337 points, 91.78% missing, 
2025-06-09 18:17:04.193 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.025s
2025-06-09 18:17:04.193 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for TSLA
2025-06-09 18:17:04.194 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.214s
2025-06-09 18:17:04.195 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:04.195 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:04.203 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:04.245 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:04.246 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:04.267 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.020s
2025-06-09 18:17:04.267 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:04.267 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.021s
2025-06-09 18:17:04.267 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:04.267 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:04.277 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:04.277 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.010s
2025-06-09 18:17:04.284 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:04.284 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:04.291 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:04.301 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:04.301 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.107s
2025-06-09 18:17:04.302 | SUCCESS  | __main__:process_data:180 | Successfully processed data for TSLA
2025-06-09 18:17:04.302 | INFO     | __main__:process_data:159 | Processing data for AVGO
2025-06-09 18:17:04.302 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:04.302 | INFO     | utils.logging:info:191 | Fetching data for AVGO from 2016-01-01 to 2025-04-19
2025-06-09 18:17:04.516 | INFO     | utils.logging:log_data_quality:270 | Data Quality - AVGO: 2337 points, 93.07% missing, 
2025-06-09 18:17:04.548 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.032s
2025-06-09 18:17:04.548 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for AVGO
2025-06-09 18:17:04.549 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.247s
2025-06-09 18:17:04.550 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:04.551 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:04.559 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:04.604 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:04.606 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:04.627 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.021s
2025-06-09 18:17:04.627 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:04.627 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.022s
2025-06-09 18:17:04.628 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:04.628 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:04.636 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:04.637 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.009s
2025-06-09 18:17:04.642 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:04.643 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:04.649 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:04.658 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:04.659 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.109s
2025-06-09 18:17:04.659 | SUCCESS  | __main__:process_data:180 | Successfully processed data for AVGO
2025-06-09 18:17:04.659 | INFO     | __main__:process_data:159 | Processing data for ADBE
2025-06-09 18:17:04.659 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:04.660 | INFO     | utils.logging:info:191 | Fetching data for ADBE from 2016-01-01 to 2025-04-19
2025-06-09 18:17:04.883 | INFO     | utils.logging:log_data_quality:270 | Data Quality - ADBE: 2337 points, 90.37% missing, 
2025-06-09 18:17:04.912 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.029s
2025-06-09 18:17:04.912 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for ADBE
2025-06-09 18:17:04.913 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.254s
2025-06-09 18:17:04.914 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:04.915 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:04.924 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:04.977 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:04.979 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:05.005 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.026s
2025-06-09 18:17:05.005 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:05.006 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.027s
2025-06-09 18:17:05.006 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:05.007 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:05.028 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:05.029 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.023s
2025-06-09 18:17:05.040 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:05.041 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:05.052 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:05.070 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:05.071 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.157s
2025-06-09 18:17:05.071 | SUCCESS  | __main__:process_data:180 | Successfully processed data for ADBE
2025-06-09 18:17:05.071 | INFO     | __main__:process_data:159 | Processing data for ASML
2025-06-09 18:17:05.072 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:05.072 | INFO     | utils.logging:info:191 | Fetching data for ASML from 2016-01-01 to 2025-04-19
2025-06-09 18:17:05.255 | INFO     | utils.logging:log_data_quality:270 | Data Quality - ASML: 2337 points, 92.42% missing, 
2025-06-09 18:17:05.296 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.041s
2025-06-09 18:17:05.297 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for ASML
2025-06-09 18:17:05.298 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.226s
2025-06-09 18:17:05.299 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:05.300 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:05.312 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:05.380 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:05.381 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:05.410 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.028s
2025-06-09 18:17:05.410 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:05.410 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.029s
2025-06-09 18:17:05.411 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:05.411 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:05.425 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:05.425 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.014s
2025-06-09 18:17:05.436 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:05.436 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:05.447 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:05.462 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:05.463 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.164s
2025-06-09 18:17:05.463 | SUCCESS  | __main__:process_data:180 | Successfully processed data for ASML
2025-06-09 18:17:05.507 | INFO     | __main__:process_data:200 | Combined data for all symbols: 23370 records
2025-06-09 18:17:05.508 | INFO     | __main__:process_data:203 | Fetching and processing VIX data
2025-06-09 18:17:05.508 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-01 to 2025-04-19
2025-06-09 18:17:05.508 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:05.705 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.028s
2025-06-09 18:17:05.705 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 VIX records
2025-06-09 18:17:05.706 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.198s
2025-06-09 18:17:05.706 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:05.707 | INFO     | utils.logging:info:191 | Processing VIX data: 2337 records
2025-06-09 18:17:05.722 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2337 records
2025-06-09 18:17:05.723 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.017s
2025-06-09 18:17:05.723 | SUCCESS  | __main__:process_data:215 | Successfully processed VIX data
2025-06-09 18:17:05.723 | INFO     | __main__:process_data:221 | Merging stock data with VIX data
2025-06-09 18:17:05.725 | INFO     | utils.logging:info:191 | [PID:481042] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 18:17:05.744 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 18:17:05.745 | SUCCESS  | __main__:process_data:226 | Successfully merged data: 23370 records
2025-06-09 18:17:05.745 | INFO     | __main__:process_data:257 | Saving processed data to data/processed/processed_data.csv
2025-06-09 18:17:07.424 | SUCCESS  | __main__:process_data:259 | Processed data saved successfully
2025-06-09 18:17:07.425 | INFO     | __main__:process_data:262 | Final processed data summary:
2025-06-09 18:17:07.425 | INFO     | __main__:process_data:263 |   - Total records: 23370
2025-06-09 18:17:07.427 | INFO     | __main__:process_data:264 |   - Unique symbols: 10
2025-06-09 18:17:07.428 | INFO     | __main__:process_data:265 |   - Date range: 2016-01-04 00:00:00 to 2025-04-17 00:00:00
2025-06-09 18:17:07.428 | INFO     | __main__:process_data:266 |   - Columns: 45
2025-06-09 18:17:07.429 | INFO     | __main__:process_data:267 |   - Sample symbols: ['AAPL', 'ADBE', 'AMZN', 'ASML', 'AVGO']
2025-06-09 18:17:07.430 | SUCCESS  | __main__:process_data:269 | Data processing completed successfully
2025-06-09 18:17:15.051 | INFO     | utils.logging:setup_logging:153 | [PID:481070] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 18:17:15.051 | INFO     | utils.logging:setup_logging:157 | [PID:481070] Worker logging setup complete - Worker ID: main
2025-06-09 18:17:15.052 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 18:17:21.172 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 18:17:21.428 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 18:17:21.429 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 18:17:21.432 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 18:17:21.464 | INFO     | utils.logging:info:191 | [PID:481070] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:17:25.260 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 18:17:25.265 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:25.301 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.035s
2025-06-09 18:17:25.302 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:25.302 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.037s
2025-06-09 18:17:25.303 | INFO     | utils.logging:info:191 | [PID:481070] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:17:25.303 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:25.329 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:25.330 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.027s
2025-06-09 18:17:25.334 | INFO     | utils.logging:info:191 | [PID:481070] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 18:17:25.354 | INFO     | utils.logging:info:191 | [PID:481070] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 18:17:25.366 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 18:17:25.367 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:25.411 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:25.460 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 18:17:25.461 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 4.029s
2025-06-09 18:17:25.461 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 18:17:25.475 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 18:17:25.475 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 18:17:25.506 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 18:17:25.514 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 18:23:56.338 | INFO     | utils.logging:setup_logging:153 | [PID:481392] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 18:23:56.342 | INFO     | utils.logging:setup_logging:157 | [PID:481392] Worker logging setup complete - Worker ID: main
2025-06-09 18:23:56.345 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 18:24:30.696 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 18:24:31.972 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 18:24:31.974 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 18:24:32.000 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 18:24:32.166 | INFO     | utils.logging:info:191 | [PID:481392] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:24:39.682 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 18:24:39.687 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:24:39.713 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.026s
2025-06-09 18:24:39.714 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:24:39.714 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.028s
2025-06-09 18:24:39.714 | INFO     | utils.logging:info:191 | [PID:481392] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:24:39.714 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:24:39.729 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:24:39.729 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.015s
2025-06-09 18:24:39.732 | INFO     | utils.logging:info:191 | [PID:481392] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 18:24:39.743 | INFO     | utils.logging:info:191 | [PID:481392] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 18:24:39.752 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 18:24:39.752 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:24:39.787 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:24:39.832 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 18:24:39.832 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 7.832s
2025-06-09 18:24:39.833 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 18:24:39.851 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 18:24:39.851 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 18:24:39.877 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 18:24:39.881 | ERROR    | __main__:train:888 | Environment creation failed: 'NoneType' object is not subscriptable
2025-06-09 18:37:20.680 | INFO     | utils.logging:setup_logging:153 | [PID:481894] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 18:37:20.681 | INFO     | utils.logging:setup_logging:157 | [PID:481894] Worker logging setup complete - Worker ID: main
2025-06-09 18:37:20.681 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 18:37:25.482 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 18:37:25.668 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 18:37:25.668 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 18:37:25.670 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 18:37:25.695 | INFO     | utils.logging:info:191 | [PID:481894] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:37:29.223 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 18:37:29.229 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:37:29.263 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.033s
2025-06-09 18:37:29.263 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:37:29.264 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.035s
2025-06-09 18:37:29.265 | INFO     | utils.logging:info:191 | [PID:481894] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:37:29.265 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:37:29.289 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:37:29.290 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.025s
2025-06-09 18:37:29.295 | INFO     | utils.logging:info:191 | [PID:481894] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 18:37:29.313 | INFO     | utils.logging:info:191 | [PID:481894] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 18:37:29.327 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 18:37:29.328 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:37:29.365 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:37:29.412 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 18:37:29.413 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 3.743s
2025-06-09 18:37:29.413 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 18:37:29.429 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 18:37:29.429 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 18:37:29.460 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 18:37:29.472 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 18:56:14.264 | INFO     | utils.logging:setup_logging:153 | [PID:483155] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 18:56:14.265 | INFO     | utils.logging:setup_logging:157 | [PID:483155] Worker logging setup complete - Worker ID: main
2025-06-09 18:56:14.265 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 18:56:18.702 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 18:56:18.894 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 18:56:18.895 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 18:56:18.898 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 18:56:18.921 | INFO     | utils.logging:info:191 | [PID:483155] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:56:21.873 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 18:56:21.878 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:56:21.910 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.032s
2025-06-09 18:56:21.910 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:56:21.911 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.034s
2025-06-09 18:56:21.912 | INFO     | utils.logging:info:191 | [PID:483155] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:56:21.912 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:56:21.928 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:56:21.928 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.016s
2025-06-09 18:56:21.930 | INFO     | utils.logging:info:191 | [PID:483155] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 18:56:21.943 | INFO     | utils.logging:info:191 | [PID:483155] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 18:56:21.951 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 18:56:21.951 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:56:21.984 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:56:22.026 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 18:56:22.026 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 3.129s
2025-06-09 18:56:22.027 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 18:56:22.039 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 18:56:22.039 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 18:56:22.060 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 18:56:22.066 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 18:59:55.279 | INFO     | utils.logging:setup_logging:153 | [PID:483457] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 18:59:55.279 | INFO     | utils.logging:setup_logging:157 | [PID:483457] Worker logging setup complete - Worker ID: main
2025-06-09 18:59:55.280 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 18:59:55.280 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 19:00:00.641 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 19:00:00.655 | INFO     | __main__:train:702 | Using best hyperparameters: {'learning_rate': 0.002190661086403672, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9067800051547014, 'soft_update_tau': 0.00108709998601217, 'alpha': 0.2655372017763429}
2025-06-09 19:00:00.833 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 19:00:00.833 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 19:00:00.836 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 19:00:00.858 | INFO     | utils.logging:info:191 | [PID:483457] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:00:04.277 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 19:00:04.282 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 19:00:04.302 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.020s
2025-06-09 19:00:04.302 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 19:00:04.303 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.021s
2025-06-09 19:00:04.303 | INFO     | utils.logging:info:191 | [PID:483457] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:00:04.303 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 19:00:04.320 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 19:00:04.321 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.017s
2025-06-09 19:00:04.323 | INFO     | utils.logging:info:191 | [PID:483457] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 19:00:04.334 | INFO     | utils.logging:info:191 | [PID:483457] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 19:00:04.343 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 19:00:04.344 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 19:00:04.377 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 19:00:04.423 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 19:00:04.423 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 3.588s
2025-06-09 19:00:04.424 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 19:00:04.437 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 19:00:04.437 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 19:00:04.457 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 19:00:04.463 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:01:26.990 | INFO     | utils.logging:setup_logging:153 | [PID:483622] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:01:26.990 | INFO     | utils.logging:setup_logging:157 | [PID:483622] Worker logging setup complete - Worker ID: main
2025-06-09 19:01:26.990 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:01:29.823 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 19:01:29.940 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 19:01:29.940 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 19:01:29.941 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 19:01:29.957 | INFO     | utils.logging:info:191 | [PID:483622] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:01:32.114 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 19:01:32.117 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 19:01:32.132 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.015s
2025-06-09 19:01:32.133 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 19:01:32.133 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.016s
2025-06-09 19:01:32.133 | INFO     | utils.logging:info:191 | [PID:483622] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:01:32.134 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 19:01:32.147 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 19:01:32.147 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.013s
2025-06-09 19:01:32.149 | INFO     | utils.logging:info:191 | [PID:483622] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 19:01:32.170 | INFO     | utils.logging:info:191 | [PID:483622] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 19:01:32.176 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 19:01:32.177 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 19:01:32.203 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 19:01:32.236 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 19:01:32.236 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 2.295s
2025-06-09 19:01:32.237 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 19:01:32.247 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 19:01:32.247 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 19:01:32.270 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 19:01:32.279 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:03:22.820 | INFO     | utils.logging:setup_logging:153 | [PID:483763] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:03:22.820 | INFO     | utils.logging:setup_logging:157 | [PID:483763] Worker logging setup complete - Worker ID: main
2025-06-09 19:03:22.820 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:03:22.820 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 19:03:25.412 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 19:03:25.413 | INFO     | __main__:train:702 | Using best hyperparameters: {'learning_rate': 0.002190661086403672, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9067800051547014, 'soft_update_tau': 0.00108709998601217, 'alpha': 0.2655372017763429}
2025-06-09 19:03:25.534 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 19:03:25.535 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 19:03:25.537 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 19:03:25.554 | INFO     | utils.logging:info:191 | [PID:483763] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:03:27.408 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 19:03:27.411 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 19:03:27.436 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.024s
2025-06-09 19:03:27.436 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 19:03:27.436 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.025s
2025-06-09 19:03:27.436 | INFO     | utils.logging:info:191 | [PID:483763] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:03:27.436 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 19:03:27.445 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 19:03:27.446 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.010s
2025-06-09 19:03:27.447 | INFO     | utils.logging:info:191 | [PID:483763] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 19:03:27.456 | INFO     | utils.logging:info:191 | [PID:483763] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 19:03:27.461 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 19:03:27.462 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 19:03:27.496 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 19:03:27.525 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 19:03:27.525 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 1.988s
2025-06-09 19:03:27.526 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 19:03:27.538 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 19:03:27.539 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 19:03:27.557 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 19:03:27.564 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:12:51.047 | INFO     | utils.logging:setup_logging:153 | [PID:484450] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:12:51.047 | INFO     | utils.logging:setup_logging:157 | [PID:484450] Worker logging setup complete - Worker ID: main
2025-06-09 19:12:51.047 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:12:51.047 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 19:12:56.304 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 19:12:56.305 | INFO     | __main__:train:702 | Using best hyperparameters: {'learning_rate': 0.002190661086403672, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9067800051547014, 'soft_update_tau': 0.00108709998601217, 'alpha': 0.2655372017763429}
2025-06-09 19:12:56.478 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 19:12:56.478 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 19:12:56.479 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 19:12:56.498 | INFO     | utils.logging:info:191 | [PID:484450] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:12:58.823 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 19:12:58.827 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 19:12:58.846 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.019s
2025-06-09 19:12:58.847 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 19:12:58.847 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.020s
2025-06-09 19:12:58.847 | INFO     | utils.logging:info:191 | [PID:484450] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:12:58.847 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 19:12:58.862 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 19:12:58.862 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.015s
2025-06-09 19:12:58.864 | INFO     | utils.logging:info:191 | [PID:484450] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 19:12:58.881 | INFO     | utils.logging:info:191 | [PID:484450] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 19:12:58.889 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 19:12:58.889 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 19:12:58.917 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 19:12:58.955 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 19:12:58.955 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 2.476s
2025-06-09 19:12:58.956 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 19:12:58.969 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 19:12:58.969 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 19:12:58.990 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 19:12:58.997 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:15:28.296 | INFO     | utils.logging:setup_logging:153 | [PID:484710] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:15:28.296 | INFO     | utils.logging:setup_logging:157 | [PID:484710] Worker logging setup complete - Worker ID: main
2025-06-09 19:15:28.296 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:15:31.034 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 19:15:31.163 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 19:15:31.163 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 19:15:31.166 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 19:15:31.184 | INFO     | utils.logging:info:191 | [PID:484710] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:15:33.094 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 19:15:33.098 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 19:15:33.115 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.017s
2025-06-09 19:15:33.115 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 19:15:33.116 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.018s
2025-06-09 19:15:33.116 | INFO     | utils.logging:info:191 | [PID:484710] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:15:33.116 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 19:15:33.127 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 19:15:33.127 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.011s
2025-06-09 19:15:33.129 | INFO     | utils.logging:info:191 | [PID:484710] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 19:15:33.137 | INFO     | utils.logging:info:191 | [PID:484710] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 19:15:33.149 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 19:15:33.149 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 19:15:33.176 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 19:15:33.214 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 19:15:33.215 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 2.049s
2025-06-09 19:15:33.215 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 19:15:33.225 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 19:15:33.225 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 19:15:33.245 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 19:15:33.252 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:16:39.171 | INFO     | utils.logging:setup_logging:153 | [PID:484887] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:16:39.171 | INFO     | utils.logging:setup_logging:157 | [PID:484887] Worker logging setup complete - Worker ID: main
2025-06-09 19:16:39.171 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:16:39.171 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 19:17:00.682 | INFO     | utils.logging:setup_logging:153 | [PID:484990] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:17:00.682 | INFO     | utils.logging:setup_logging:157 | [PID:484990] Worker logging setup complete - Worker ID: main
2025-06-09 19:17:00.682 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:17:00.682 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 19:17:05.845 | INFO     | utils.logging:setup_logging:153 | [PID:485013] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:17:05.845 | INFO     | utils.logging:setup_logging:157 | [PID:485013] Worker logging setup complete - Worker ID: main
2025-06-09 19:17:05.845 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:17:05.845 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 19:17:09.544 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 19:17:09.545 | INFO     | __main__:train:702 | Using best hyperparameters: {'learning_rate': 0.002190661086403672, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9067800051547014, 'soft_update_tau': 0.00108709998601217, 'alpha': 0.2655372017763429}
2025-06-09 19:17:09.686 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 19:17:09.686 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 19:17:09.687 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 19:17:09.704 | INFO     | utils.logging:info:191 | [PID:485013] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:17:11.711 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 19:17:11.714 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 19:17:11.728 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.013s
2025-06-09 19:17:11.728 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 19:17:11.728 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.014s
2025-06-09 19:17:11.729 | INFO     | utils.logging:info:191 | [PID:485013] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:17:11.729 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 19:17:11.740 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 19:17:11.740 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.012s
2025-06-09 19:17:11.742 | INFO     | utils.logging:info:191 | [PID:485013] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 19:17:11.757 | INFO     | utils.logging:info:191 | [PID:485013] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 19:17:11.764 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 19:17:11.764 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 19:17:11.790 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 19:17:11.819 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 19:17:11.819 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 2.132s
2025-06-09 19:17:11.820 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 19:17:11.830 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 19:17:11.830 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 19:17:11.848 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 19:17:11.854 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:18:45.427 | INFO     | utils.logging:setup_logging:153 | [PID:485139] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:18:45.427 | INFO     | utils.logging:setup_logging:157 | [PID:485139] Worker logging setup complete - Worker ID: main
2025-06-09 19:18:45.428 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:18:45.428 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 19:18:49.398 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 19:18:49.398 | INFO     | __main__:train:702 | Using best hyperparameters: {'learning_rate': 0.002190661086403672, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9067800051547014, 'soft_update_tau': 0.00108709998601217, 'alpha': 0.2655372017763429}
2025-06-09 19:18:49.544 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 19:18:49.544 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 19:18:49.546 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 19:18:49.566 | INFO     | utils.logging:info:191 | [PID:485139] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:18:51.589 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 19:18:51.592 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 19:18:51.608 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.016s
2025-06-09 19:18:51.608 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 19:18:51.609 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.017s
2025-06-09 19:18:51.609 | INFO     | utils.logging:info:191 | [PID:485139] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:18:51.610 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 19:18:51.619 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 19:18:51.620 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.011s
2025-06-09 19:18:51.621 | INFO     | utils.logging:info:191 | [PID:485139] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 19:18:51.630 | INFO     | utils.logging:info:191 | [PID:485139] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 19:18:51.636 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 19:18:51.636 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 19:18:51.660 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 19:18:51.703 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 19:18:51.704 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 2.158s
2025-06-09 19:18:51.705 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 19:18:51.728 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 19:18:51.729 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 19:18:51.757 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 19:18:51.764 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:21:50.698 | INFO     | utils.logging:setup_logging:153 | [PID:485366] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:21:50.698 | INFO     | utils.logging:setup_logging:157 | [PID:485366] Worker logging setup complete - Worker ID: main
2025-06-09 19:21:50.698 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:21:50.698 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 19:21:53.628 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 19:21:53.628 | INFO     | __main__:train:702 | Using best hyperparameters: {'learning_rate': 0.002190661086403672, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9067800051547014, 'soft_update_tau': 0.00108709998601217, 'alpha': 0.2655372017763429}
2025-06-09 19:21:53.776 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 19:21:53.776 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 19:21:53.779 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 19:21:53.800 | INFO     | utils.logging:info:191 | [PID:485366] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:21:56.116 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 19:21:56.120 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 19:21:56.152 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.032s
2025-06-09 19:21:56.153 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 19:21:56.153 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.034s
2025-06-09 19:21:56.154 | INFO     | utils.logging:info:191 | [PID:485366] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:21:56.154 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 19:21:56.171 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 19:21:56.171 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.018s
2025-06-09 19:21:56.176 | INFO     | utils.logging:info:191 | [PID:485366] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 19:21:56.188 | INFO     | utils.logging:info:191 | [PID:485366] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 19:21:56.199 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 19:21:56.200 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 19:21:56.233 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 19:21:56.269 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 19:21:56.270 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 2.491s
2025-06-09 19:21:56.271 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 19:21:56.282 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 19:21:56.282 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 19:21:56.302 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 19:21:56.310 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:22:45.662 | INFO     | utils.logging:setup_logging:153 | [PID:485461] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:22:45.662 | INFO     | utils.logging:setup_logging:157 | [PID:485461] Worker logging setup complete - Worker ID: main
2025-06-09 19:22:45.662 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:22:45.662 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 19:22:48.498 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 19:22:48.498 | INFO     | __main__:train:702 | Using best hyperparameters: {'learning_rate': 0.002190661086403672, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9067800051547014, 'soft_update_tau': 0.00108709998601217, 'alpha': 0.2655372017763429}
2025-06-09 19:22:48.630 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 19:22:48.630 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 19:22:48.632 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 19:22:48.650 | INFO     | utils.logging:info:191 | [PID:485461] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:22:50.730 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 19:22:50.734 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 19:22:50.749 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.015s
2025-06-09 19:22:50.749 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 19:22:50.750 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.016s
2025-06-09 19:22:50.750 | INFO     | utils.logging:info:191 | [PID:485461] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:22:50.751 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 19:22:50.759 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 19:22:50.760 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.009s
2025-06-09 19:22:50.761 | INFO     | utils.logging:info:191 | [PID:485461] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 19:22:50.771 | INFO     | utils.logging:info:191 | [PID:485461] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 19:22:50.779 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 19:22:50.780 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 19:22:50.810 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 19:22:50.862 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 19:22:50.862 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 2.230s
2025-06-09 19:22:50.863 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 19:22:50.875 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 19:22:50.875 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 19:22:50.894 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 19:22:50.900 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:23:04.156 | INFO     | utils.logging:setup_logging:153 | [PID:485532] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:23:04.156 | INFO     | utils.logging:setup_logging:157 | [PID:485532] Worker logging setup complete - Worker ID: main
2025-06-09 19:23:04.156 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:23:04.156 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 19:23:07.342 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 19:23:07.343 | INFO     | __main__:train:702 | Using best hyperparameters: {'learning_rate': 0.002190661086403672, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9067800051547014, 'soft_update_tau': 0.00108709998601217, 'alpha': 0.2655372017763429}
2025-06-09 19:23:07.483 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 19:23:07.484 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 19:23:07.487 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 19:23:07.507 | INFO     | utils.logging:info:191 | [PID:485532] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:23:09.926 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 19:23:09.931 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 19:23:09.963 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.031s
2025-06-09 19:23:09.963 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 19:23:09.963 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.032s
2025-06-09 19:23:09.964 | INFO     | utils.logging:info:191 | [PID:485532] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:23:09.964 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 19:23:09.975 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 19:23:09.975 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.011s
2025-06-09 19:23:09.977 | INFO     | utils.logging:info:191 | [PID:485532] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 19:23:09.988 | INFO     | utils.logging:info:191 | [PID:485532] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 19:23:09.997 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 19:23:09.997 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 19:23:10.027 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 19:23:10.058 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 19:23:10.058 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 2.572s
2025-06-09 19:23:10.059 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 19:23:10.069 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 19:23:10.069 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 19:23:10.090 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 19:23:10.098 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:23:23.241 | INFO     | utils.logging:setup_logging:153 | [PID:485609] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:23:23.242 | INFO     | utils.logging:setup_logging:157 | [PID:485609] Worker logging setup complete - Worker ID: main
2025-06-09 19:23:23.242 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:23:23.242 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 19:23:26.393 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 19:23:26.394 | INFO     | __main__:train:702 | Using best hyperparameters: {'learning_rate': 0.002190661086403672, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9067800051547014, 'soft_update_tau': 0.00108709998601217, 'alpha': 0.2655372017763429}
2025-06-09 19:23:26.537 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 19:23:26.537 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 19:23:26.538 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 19:23:26.557 | INFO     | utils.logging:info:191 | [PID:485609] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:23:28.729 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 19:23:28.733 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 19:23:28.749 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.016s
2025-06-09 19:23:28.749 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 19:23:28.749 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.017s
2025-06-09 19:23:28.749 | INFO     | utils.logging:info:191 | [PID:485609] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:23:28.750 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 19:23:28.760 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 19:23:28.760 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.010s
2025-06-09 19:23:28.761 | INFO     | utils.logging:info:191 | [PID:485609] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 19:23:28.772 | INFO     | utils.logging:info:191 | [PID:485609] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 19:23:28.778 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 19:23:28.778 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 19:23:28.799 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 19:23:28.827 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 19:23:28.827 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 2.289s
2025-06-09 19:23:28.828 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 19:23:28.837 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 19:23:28.837 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 19:23:28.856 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 19:23:28.863 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:24:02.097 | INFO     | utils.logging:setup_logging:153 | [PID:485697] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:24:02.097 | INFO     | utils.logging:setup_logging:157 | [PID:485697] Worker logging setup complete - Worker ID: main
2025-06-09 19:24:02.097 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:24:02.097 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 19:24:05.286 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 19:24:05.286 | INFO     | __main__:train:702 | Using best hyperparameters: {'learning_rate': 0.002190661086403672, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9067800051547014, 'soft_update_tau': 0.00108709998601217, 'alpha': 0.2655372017763429}
2025-06-09 19:24:05.431 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 19:24:05.431 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 19:24:05.433 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 19:24:05.450 | INFO     | utils.logging:info:191 | [PID:485697] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:24:07.733 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 19:24:07.737 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 19:24:07.761 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.024s
2025-06-09 19:24:07.762 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 19:24:07.763 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.026s
2025-06-09 19:24:07.763 | INFO     | utils.logging:info:191 | [PID:485697] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:24:07.763 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 19:24:07.777 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 19:24:07.777 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.014s
2025-06-09 19:24:07.780 | INFO     | utils.logging:info:191 | [PID:485697] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 19:24:07.802 | INFO     | utils.logging:info:191 | [PID:485697] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 19:24:07.810 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 19:24:07.811 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 19:24:07.842 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 19:24:07.883 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 19:24:07.883 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 2.450s
2025-06-09 19:24:07.884 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 19:24:07.896 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 19:24:07.896 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 19:24:07.923 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 19:24:07.930 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 19:28:16.536 | INFO     | utils.logging:setup_logging:153 | [PID:486054] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:28:16.537 | INFO     | utils.logging:setup_logging:157 | [PID:486054] Worker logging setup complete - Worker ID: main
2025-06-09 19:28:16.537 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:28:16.537 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 19:28:19.677 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 19:28:19.678 | INFO     | __main__:train:702 | Using best hyperparameters: {'learning_rate': 0.002190661086403672, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9067800051547014, 'soft_update_tau': 0.00108709998601217, 'alpha': 0.2655372017763429}
2025-06-09 19:28:19.812 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 19:28:19.812 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 19:28:19.815 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 19:28:19.834 | INFO     | utils.logging:info:191 | [PID:486054] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:28:21.981 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 19:28:21.985 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 19:28:22.001 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.016s
2025-06-09 19:28:22.001 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 19:28:22.001 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.017s
2025-06-09 19:28:22.002 | INFO     | utils.logging:info:191 | [PID:486054] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:28:22.002 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 19:28:22.011 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 19:28:22.012 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.010s
2025-06-09 19:28:22.013 | INFO     | utils.logging:info:191 | [PID:486054] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 19:28:22.025 | INFO     | utils.logging:info:191 | [PID:486054] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 19:28:22.033 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 19:28:22.034 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 19:28:22.057 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 19:28:22.088 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 19:28:22.088 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 2.273s
2025-06-09 19:28:22.088 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 19:28:22.097 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 19:28:22.097 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 19:28:22.120 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 19:28:22.127 | ERROR    | __main__:train:888 | Environment creation failed: 'NoneType' object is not subscriptable
2025-06-09 19:28:37.581 | INFO     | utils.logging:setup_logging:153 | [PID:486134] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:28:37.582 | INFO     | utils.logging:setup_logging:157 | [PID:486134] Worker logging setup complete - Worker ID: main
2025-06-09 19:28:37.582 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:28:37.582 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 19:28:40.668 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 19:28:40.668 | INFO     | __main__:train:702 | Using best hyperparameters: {'learning_rate': 0.002190661086403672, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9067800051547014, 'soft_update_tau': 0.00108709998601217, 'alpha': 0.2655372017763429}
2025-06-09 19:28:40.804 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 19:28:40.804 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 19:28:40.807 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 19:28:40.823 | INFO     | utils.logging:info:191 | [PID:486134] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:28:43.953 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 19:28:43.957 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 19:28:43.974 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.017s
2025-06-09 19:28:43.974 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 19:28:43.974 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.018s
2025-06-09 19:28:43.975 | INFO     | utils.logging:info:191 | [PID:486134] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:28:43.975 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 19:28:43.985 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 19:28:43.985 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.011s
2025-06-09 19:28:43.987 | INFO     | utils.logging:info:191 | [PID:486134] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 19:28:43.996 | INFO     | utils.logging:info:191 | [PID:486134] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 19:28:44.002 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 19:28:44.002 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 19:28:44.026 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 19:28:44.062 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 19:28:44.062 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 3.256s
2025-06-09 19:28:44.063 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 19:28:44.073 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 19:28:44.073 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 19:28:44.091 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 19:28:44.097 | ERROR    | __main__:train:888 | Environment creation failed: 'NoneType' object is not subscriptable
2025-06-09 19:30:40.414 | INFO     | utils.logging:setup_logging:153 | [PID:486360] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:30:40.414 | INFO     | utils.logging:setup_logging:157 | [PID:486360] Worker logging setup complete - Worker ID: main
2025-06-09 19:30:40.414 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:30:40.414 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 19:30:42.926 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 19:30:42.926 | INFO     | __main__:train:702 | Using best hyperparameters: {'learning_rate': 0.002190661086403672, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9067800051547014, 'soft_update_tau': 0.00108709998601217, 'alpha': 0.2655372017763429}
2025-06-09 19:30:43.052 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 19:30:43.052 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 19:30:43.054 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 19:30:43.072 | INFO     | utils.logging:info:191 | [PID:486360] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:30:45.023 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 19:30:45.027 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 19:30:45.046 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.019s
2025-06-09 19:30:45.047 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 19:30:45.047 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.020s
2025-06-09 19:30:45.047 | INFO     | utils.logging:info:191 | [PID:486360] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:30:45.048 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 19:30:45.057 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 19:30:45.058 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.010s
2025-06-09 19:30:45.059 | INFO     | utils.logging:info:191 | [PID:486360] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 19:30:45.074 | INFO     | utils.logging:info:191 | [PID:486360] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 19:30:45.081 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 19:30:45.081 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 19:30:45.107 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 19:30:45.147 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 19:30:45.148 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 2.094s
2025-06-09 19:30:45.148 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 19:30:45.158 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 19:30:45.158 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 19:30:45.177 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 19:30:45.182 | ERROR    | __main__:train:888 | Environment creation failed: 'NoneType' object is not subscriptable
2025-06-09 19:48:03.639 | INFO     | utils.logging:setup_logging:153 | [PID:488560] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 19:48:03.639 | INFO     | utils.logging:setup_logging:157 | [PID:488560] Worker logging setup complete - Worker ID: main
2025-06-09 19:48:03.640 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 19:48:08.769 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 19:48:08.982 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 19:48:08.982 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 19:48:08.984 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 19:48:09.008 | INFO     | utils.logging:info:191 | [PID:488560] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:48:12.492 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 19:48:12.499 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 19:48:12.536 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.037s
2025-06-09 19:48:12.537 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 19:48:12.537 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.039s
2025-06-09 19:48:12.538 | INFO     | utils.logging:info:191 | [PID:488560] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 19:48:12.538 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 19:48:12.563 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 19:48:12.563 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.026s
2025-06-09 19:48:12.566 | INFO     | utils.logging:info:191 | [PID:488560] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 19:48:12.594 | INFO     | utils.logging:info:191 | [PID:488560] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 19:48:12.607 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 19:48:12.608 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 19:48:12.656 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 19:48:12.701 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 19:48:12.701 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 3.717s
2025-06-09 19:48:12.702 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 19:48:12.714 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 19:48:12.715 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 19:48:12.742 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 19:48:12.753 | ERROR    | __main__:train:888 | Environment creation failed: 'Timestamp' object has no attribute 'unique'
2025-06-09 20:05:15.281 | INFO     | utils.logging:setup_logging:153 | [PID:490147] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 20:05:15.282 | INFO     | utils.logging:setup_logging:157 | [PID:490147] Worker logging setup complete - Worker ID: main
2025-06-09 20:05:15.282 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 20:05:15.283 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 20:05:22.323 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 20:05:22.576 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 20:05:22.577 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 20:05:22.580 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 20:05:22.608 | INFO     | utils.logging:info:191 | [PID:490147] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:05:26.595 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 20:05:26.600 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 20:05:26.645 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.044s
2025-06-09 20:05:26.645 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 20:05:26.646 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.046s
2025-06-09 20:05:26.646 | INFO     | utils.logging:info:191 | [PID:490147] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:05:26.647 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 20:05:26.663 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 20:05:26.664 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.018s
2025-06-09 20:05:26.667 | INFO     | utils.logging:info:191 | [PID:490147] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 20:05:26.683 | INFO     | utils.logging:info:191 | [PID:490147] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 20:05:26.695 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 20:05:26.696 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 20:05:26.739 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 20:05:26.783 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 20:05:26.783 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 4.203s
2025-06-09 20:05:26.784 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 20:05:26.796 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 20:05:26.796 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 20:05:26.823 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 20:05:26.838 | ERROR    | __main__:train:888 | Environment creation failed: 'Timestamp' object has no attribute 'unique'
2025-06-09 20:21:17.065 | INFO     | utils.logging:setup_logging:153 | [PID:491304] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 20:21:17.067 | INFO     | utils.logging:setup_logging:157 | [PID:491304] Worker logging setup complete - Worker ID: main
2025-06-09 20:21:17.071 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 20:21:17.075 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 20:22:03.755 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 20:22:05.143 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 20:22:05.146 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 20:22:05.156 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 20:22:05.352 | INFO     | utils.logging:info:191 | [PID:491304] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:22:29.582 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 20:22:29.616 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 20:22:29.768 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.150s
2025-06-09 20:22:29.770 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 20:22:29.772 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.158s
2025-06-09 20:22:29.775 | INFO     | utils.logging:info:191 | [PID:491304] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:22:29.776 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 20:22:29.872 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 20:22:29.875 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.100s
2025-06-09 20:22:29.886 | INFO     | utils.logging:info:191 | [PID:491304] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 20:22:29.960 | INFO     | utils.logging:info:191 | [PID:491304] DataProcessor.merge_with_vix: Excluded text-based VIX features from tech_indicator_list: ['vix_regime']
2025-06-09 20:22:30.026 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 20:22:30.030 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 20:22:30.230 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 20:22:30.555 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 20:22:30.557 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 25.401s
2025-06-09 20:22:30.560 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 20:22:30.646 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 20:22:30.648 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 20:22:30.808 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 20:22:30.906 | ERROR    | __main__:train:888 | Environment creation failed: 'Timestamp' object has no attribute 'unique'
2025-06-09 20:25:36.861 | INFO     | utils.logging:setup_logging:153 | [PID:491699] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 20:25:36.863 | INFO     | utils.logging:setup_logging:157 | [PID:491699] Worker logging setup complete - Worker ID: main
2025-06-09 20:25:36.866 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 20:25:36.868 | DEBUG    | __main__:cli:76 | Configuration loaded from: default
2025-06-09 20:25:42.717 | INFO     | __main__:process_data:137 | Starting data processing
2025-06-09 20:25:42.723 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 20:25:42.724 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 20:25:42.727 | INFO     | __main__:process_data:157 | Fetching and processing data for 10 symbols...
2025-06-09 20:25:42.730 | INFO     | __main__:process_data:159 | Processing data for AAPL
2025-06-09 20:25:42.961 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.226s
2025-06-09 20:25:42.964 | INFO     | utils.logging:info:191 | Using cached data for AAPL
2025-06-09 20:25:42.966 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.233s
2025-06-09 20:25:42.971 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 20:25:42.975 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 20:25:43.028 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:43.383 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 20:25:43.390 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 20:25:43.528 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.136s
2025-06-09 20:25:43.530 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 20:25:43.531 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.142s
2025-06-09 20:25:43.532 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:43.534 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 20:25:43.642 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 20:25:43.644 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.111s
2025-06-09 20:25:43.696 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.merge_with_vix: Excluded text-based VIX features from tech_indicator_list: ['vix_regime']
2025-06-09 20:25:43.744 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 20:25:43.749 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 20:25:43.845 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 20:25:43.967 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 20:25:43.969 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.999s
2025-06-09 20:25:43.973 | SUCCESS  | __main__:process_data:180 | Successfully processed data for AAPL
2025-06-09 20:25:43.975 | INFO     | __main__:process_data:159 | Processing data for MSFT
2025-06-09 20:25:44.219 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.239s
2025-06-09 20:25:44.221 | INFO     | utils.logging:info:191 | Using cached data for MSFT
2025-06-09 20:25:44.229 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.249s
2025-06-09 20:25:44.239 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 20:25:44.244 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 20:25:44.317 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:44.880 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 20:25:44.890 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 20:25:45.117 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.224s
2025-06-09 20:25:45.119 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 20:25:45.121 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.231s
2025-06-09 20:25:45.123 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:45.125 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 20:25:45.227 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 20:25:45.229 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.106s
2025-06-09 20:25:45.280 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.merge_with_vix: Excluded text-based VIX features from tech_indicator_list: ['vix_regime']
2025-06-09 20:25:45.328 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 20:25:45.332 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 20:25:45.422 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 20:25:45.554 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 20:25:45.557 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 1.318s
2025-06-09 20:25:45.558 | SUCCESS  | __main__:process_data:180 | Successfully processed data for MSFT
2025-06-09 20:25:45.562 | INFO     | __main__:process_data:159 | Processing data for GOOGL
2025-06-09 20:25:45.742 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.176s
2025-06-09 20:25:45.745 | INFO     | utils.logging:info:191 | Using cached data for GOOGL
2025-06-09 20:25:45.748 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.182s
2025-06-09 20:25:45.755 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 20:25:45.760 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 20:25:45.802 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:46.180 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 20:25:46.190 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 20:25:46.362 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.170s
2025-06-09 20:25:46.364 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 20:25:46.365 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.176s
2025-06-09 20:25:46.368 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:46.373 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 20:25:46.466 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 20:25:46.468 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.101s
2025-06-09 20:25:46.508 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.merge_with_vix: Excluded text-based VIX features from tech_indicator_list: ['vix_regime']
2025-06-09 20:25:46.543 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 20:25:46.546 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 20:25:46.621 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 20:25:46.755 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 20:25:46.757 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 1.003s
2025-06-09 20:25:46.759 | SUCCESS  | __main__:process_data:180 | Successfully processed data for GOOGL
2025-06-09 20:25:46.761 | INFO     | __main__:process_data:159 | Processing data for AMZN
2025-06-09 20:25:46.926 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.162s
2025-06-09 20:25:46.927 | INFO     | utils.logging:info:191 | Using cached data for AMZN
2025-06-09 20:25:46.928 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.165s
2025-06-09 20:25:46.932 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 20:25:46.937 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 20:25:46.987 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:47.335 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 20:25:47.341 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 20:25:47.492 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.147s
2025-06-09 20:25:47.494 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 20:25:47.495 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.155s
2025-06-09 20:25:47.497 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:47.499 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 20:25:47.598 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 20:25:47.600 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.102s
2025-06-09 20:25:47.634 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.merge_with_vix: Excluded text-based VIX features from tech_indicator_list: ['vix_regime']
2025-06-09 20:25:47.672 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 20:25:47.675 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 20:25:47.749 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 20:25:47.843 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 20:25:47.845 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.913s
2025-06-09 20:25:47.846 | SUCCESS  | __main__:process_data:180 | Successfully processed data for AMZN
2025-06-09 20:25:47.849 | INFO     | __main__:process_data:159 | Processing data for META
2025-06-09 20:25:47.984 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.133s
2025-06-09 20:25:47.985 | INFO     | utils.logging:info:191 | Using cached data for META
2025-06-09 20:25:47.988 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.137s
2025-06-09 20:25:47.995 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 20:25:47.998 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 20:25:48.048 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:48.454 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 20:25:48.464 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 20:25:48.637 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.171s
2025-06-09 20:25:48.639 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 20:25:48.640 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.176s
2025-06-09 20:25:48.642 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:48.644 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 20:25:48.724 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 20:25:48.725 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.083s
2025-06-09 20:25:48.763 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.merge_with_vix: Excluded text-based VIX features from tech_indicator_list: ['vix_regime']
2025-06-09 20:25:48.795 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 20:25:48.797 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 20:25:48.853 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 20:25:48.964 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 20:25:48.967 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.972s
2025-06-09 20:25:48.968 | SUCCESS  | __main__:process_data:180 | Successfully processed data for META
2025-06-09 20:25:48.971 | INFO     | __main__:process_data:159 | Processing data for NVDA
2025-06-09 20:25:49.123 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.149s
2025-06-09 20:25:49.125 | INFO     | utils.logging:info:191 | Using cached data for NVDA
2025-06-09 20:25:49.126 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.153s
2025-06-09 20:25:49.128 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 20:25:49.131 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 20:25:49.185 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:49.518 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 20:25:49.524 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 20:25:49.653 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.127s
2025-06-09 20:25:49.655 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 20:25:49.656 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.132s
2025-06-09 20:25:49.659 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:49.660 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 20:25:49.736 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 20:25:49.737 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.078s
2025-06-09 20:25:49.774 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.merge_with_vix: Excluded text-based VIX features from tech_indicator_list: ['vix_regime']
2025-06-09 20:25:49.808 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 20:25:49.810 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 20:25:49.880 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 20:25:49.969 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 20:25:49.971 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.843s
2025-06-09 20:25:49.972 | SUCCESS  | __main__:process_data:180 | Successfully processed data for NVDA
2025-06-09 20:25:49.975 | INFO     | __main__:process_data:159 | Processing data for TSLA
2025-06-09 20:25:50.093 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.116s
2025-06-09 20:25:50.094 | INFO     | utils.logging:info:191 | Using cached data for TSLA
2025-06-09 20:25:50.097 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.120s
2025-06-09 20:25:50.102 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 20:25:50.107 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 20:25:50.142 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:50.458 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 20:25:50.471 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 20:25:50.606 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.133s
2025-06-09 20:25:50.607 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 20:25:50.609 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.138s
2025-06-09 20:25:50.611 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:50.613 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 20:25:50.705 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 20:25:50.707 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.096s
2025-06-09 20:25:50.750 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.merge_with_vix: Excluded text-based VIX features from tech_indicator_list: ['vix_regime']
2025-06-09 20:25:50.784 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 20:25:50.787 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 20:25:50.868 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 20:25:50.971 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 20:25:50.973 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.871s
2025-06-09 20:25:50.976 | SUCCESS  | __main__:process_data:180 | Successfully processed data for TSLA
2025-06-09 20:25:50.979 | INFO     | __main__:process_data:159 | Processing data for AVGO
2025-06-09 20:25:51.113 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.133s
2025-06-09 20:25:51.115 | INFO     | utils.logging:info:191 | Using cached data for AVGO
2025-06-09 20:25:51.117 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.137s
2025-06-09 20:25:51.121 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 20:25:51.124 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 20:25:51.176 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:51.649 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 20:25:51.658 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 20:25:51.815 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.155s
2025-06-09 20:25:51.817 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 20:25:51.819 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.161s
2025-06-09 20:25:51.821 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:51.823 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 20:25:51.894 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 20:25:51.896 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.075s
2025-06-09 20:25:51.936 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.merge_with_vix: Excluded text-based VIX features from tech_indicator_list: ['vix_regime']
2025-06-09 20:25:51.976 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 20:25:51.979 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 20:25:52.060 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 20:25:52.179 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 20:25:52.182 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 1.061s
2025-06-09 20:25:52.184 | SUCCESS  | __main__:process_data:180 | Successfully processed data for AVGO
2025-06-09 20:25:52.187 | INFO     | __main__:process_data:159 | Processing data for ADBE
2025-06-09 20:25:52.324 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.134s
2025-06-09 20:25:52.326 | INFO     | utils.logging:info:191 | Using cached data for ADBE
2025-06-09 20:25:52.328 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.139s
2025-06-09 20:25:52.331 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 20:25:52.337 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 20:25:52.379 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:52.719 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 20:25:52.727 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 20:25:52.885 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.157s
2025-06-09 20:25:52.887 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 20:25:52.890 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.163s
2025-06-09 20:25:52.893 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:52.894 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 20:25:52.973 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 20:25:52.975 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.083s
2025-06-09 20:25:53.007 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.merge_with_vix: Excluded text-based VIX features from tech_indicator_list: ['vix_regime']
2025-06-09 20:25:53.041 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 20:25:53.045 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 20:25:53.122 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 20:25:53.218 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 20:25:53.220 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.889s
2025-06-09 20:25:53.221 | SUCCESS  | __main__:process_data:180 | Successfully processed data for ADBE
2025-06-09 20:25:53.223 | INFO     | __main__:process_data:159 | Processing data for ASML
2025-06-09 20:25:53.363 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.139s
2025-06-09 20:25:53.365 | INFO     | utils.logging:info:191 | Using cached data for ASML
2025-06-09 20:25:53.368 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.145s
2025-06-09 20:25:53.371 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 20:25:53.373 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 20:25:53.411 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:53.828 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 20:25:53.837 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 20:25:53.977 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.138s
2025-06-09 20:25:53.979 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 20:25:53.980 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.144s
2025-06-09 20:25:53.984 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:53.985 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 20:25:54.058 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 20:25:54.060 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.076s
2025-06-09 20:25:54.089 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.merge_with_vix: Excluded text-based VIX features from tech_indicator_list: ['vix_regime']
2025-06-09 20:25:54.118 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 20:25:54.120 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 20:25:54.181 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 20:25:54.265 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 20:25:54.268 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.897s
2025-06-09 20:25:54.269 | SUCCESS  | __main__:process_data:180 | Successfully processed data for ASML
2025-06-09 20:25:54.476 | INFO     | __main__:process_data:200 | Combined data for all symbols: 23370 records
2025-06-09 20:25:54.478 | INFO     | __main__:process_data:203 | Fetching and processing VIX data
2025-06-09 20:25:54.482 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-01 to 2025-04-19
2025-06-09 20:25:54.613 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.129s
2025-06-09 20:25:54.615 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 20:25:54.617 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.135s
2025-06-09 20:25:54.618 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:25:54.620 | INFO     | utils.logging:info:191 | Processing VIX data: 2337 records
2025-06-09 20:25:54.719 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2337 records
2025-06-09 20:25:54.721 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.103s
2025-06-09 20:25:54.723 | SUCCESS  | __main__:process_data:215 | Successfully processed VIX data
2025-06-09 20:25:54.725 | INFO     | __main__:process_data:221 | Merging stock data with VIX data
2025-06-09 20:25:54.746 | INFO     | utils.logging:info:191 | [PID:491699] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 20:25:54.812 | INFO     | utils.logging:info:191 | [PID:491699] DataProcessor.merge_with_vix: Excluded text-based VIX features from tech_indicator_list: ['vix_regime']
2025-06-09 20:25:54.867 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 20:25:54.869 | SUCCESS  | __main__:process_data:226 | Successfully merged data: 23370 records
2025-06-09 20:25:54.873 | INFO     | __main__:process_data:257 | Saving processed data to data/processed/processed_data.csv
2025-06-09 20:26:04.539 | SUCCESS  | __main__:process_data:259 | Processed data saved successfully
2025-06-09 20:26:04.540 | INFO     | __main__:process_data:262 | Final processed data summary:
2025-06-09 20:26:04.543 | INFO     | __main__:process_data:263 |   - Total records: 23370
2025-06-09 20:26:04.551 | INFO     | __main__:process_data:264 |   - Unique symbols: 10
2025-06-09 20:26:04.558 | INFO     | __main__:process_data:265 |   - Date range: 2016-01-04 00:00:00 to 2025-04-17 00:00:00
2025-06-09 20:26:04.560 | INFO     | __main__:process_data:266 |   - Columns: 45
2025-06-09 20:26:04.566 | INFO     | __main__:process_data:267 |   - Sample symbols: ['AAPL', 'ADBE', 'AMZN', 'ASML', 'AVGO']
2025-06-09 20:26:04.568 | SUCCESS  | __main__:process_data:269 | Data processing completed successfully
2025-06-09 20:27:58.787 | INFO     | utils.logging:setup_logging:153 | [PID:491922] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 20:27:58.789 | INFO     | utils.logging:setup_logging:157 | [PID:491922] Worker logging setup complete - Worker ID: main
2025-06-09 20:27:58.790 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 20:28:27.520 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 20:28:28.729 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 20:28:28.732 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 20:28:28.751 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 20:28:28.910 | INFO     | utils.logging:info:191 | [PID:491922] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:28:51.386 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 20:28:51.416 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 20:28:51.540 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.122s
2025-06-09 20:28:51.542 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 20:28:51.545 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.130s
2025-06-09 20:28:51.546 | INFO     | utils.logging:info:191 | [PID:491922] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 20:28:51.549 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 20:28:51.634 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 20:28:51.635 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.089s
2025-06-09 20:28:51.648 | INFO     | utils.logging:info:191 | [PID:491922] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 20:28:51.711 | INFO     | utils.logging:info:191 | [PID:491922] DataProcessor.merge_with_vix: Excluded text-based VIX features from tech_indicator_list: ['vix_regime']
2025-06-09 20:28:51.777 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 20:28:51.780 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 20:28:51.965 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 20:28:52.157 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 20:28:52.159 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 23.408s
2025-06-09 20:28:52.162 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 20:28:52.232 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 20:28:52.234 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 20:28:52.368 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 20:28:52.429 | ERROR    | __main__:train:888 | Environment creation failed: 'Timestamp' object has no attribute 'unique'
